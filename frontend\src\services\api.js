import axios from "axios";

const API_BASE_URL = "http://localhost:5000/api";

// Cache za proizvode
let productsCache = null;
let cacheTimestamp = null;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minuta

// Funkcija za obradu proizvoda (ista logika kao u backend-u)
const processProducts = (products, filters = {}) => {
  let processedProducts = products.map((product) => {
    const processed = { ...product };

    // Uvećaj cenu za 10% za monitore
    const categoryName = product.categoryName || "";
    if (categoryName.toLowerCase() === "monitori") {
      const originalPrice = parseFloat(product.price || 0);
      processed.price = Math.round(originalPrice * 1.1 * 100) / 100;
    }

    // Zameni "brzina" sa "performanse" u opisu (case-insensitive)
    if (processed.description) {
      processed.description = processed.description.replace(
        /\bbrzina\b/gi,
        "performanse"
      );
    }

    return processed;
  });

  // Filtriranje po kategoriji
  if (filters.category) {
    processedProducts = processedProducts.filter(
      (p) =>
        (p.categoryName || "").toLowerCase() === filters.category.toLowerCase()
    );
  }

  // Pretraga po nazivu
  if (filters.search) {
    const searchLower = filters.search.toLowerCase();
    processedProducts = processedProducts.filter(
      (p) =>
        (p.naziv || "").toLowerCase().includes(searchLower) ||
        (p.description || "").toLowerCase().includes(searchLower)
    );
  }

  return processedProducts;
};

// Kreiranje axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

// Interceptor za dodavanje JWT tokena u zahteve
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("jwt_token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Interceptor za rukovanje odgovorima
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Ukloni token i preusmeri na login
      localStorage.removeItem("jwt_token");
      window.location.href = "/login";
    }
    return Promise.reject(error);
  }
);

// API funkcije
export const authAPI = {
  login: async (username, password) => {
    // Pozivamo eksterni API preko Vite proxy-ja
    const response = await axios.post("/external-api/login", {
      username,
      password,
    });
    return response.data;
  },
};

// Funkcija za čišćenje cache-a
export const clearProductsCache = () => {
  productsCache = null;
  cacheTimestamp = null;
};

export const productsAPI = {
  getProducts: async (filters = {}) => {
    const token = localStorage.getItem("jwt_token");
    if (!token) {
      throw new Error("No authentication token found");
    }

    // Proveri cache
    const now = Date.now();
    if (
      productsCache &&
      cacheTimestamp &&
      now - cacheTimestamp < CACHE_DURATION
    ) {
      // Koristi cache i filtriraj lokalno
      return processProducts(productsCache, filters);
    }

    // Dohvati sve proizvode sa eksternog API-ja
    const response = await axios.get("/external-api/products", {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    // Sačuvaj u cache
    productsCache = response.data;
    cacheTimestamp = now;

    // Obradi i filtriraj podatke lokalno
    return processProducts(response.data, filters);
  },

  getProduct: async (id) => {
    const token = localStorage.getItem("jwt_token");
    if (!token) {
      throw new Error("No authentication token found");
    }

    // Prvo pokušaj da koristiš cache ako postoji
    if (
      productsCache &&
      cacheTimestamp &&
      Date.now() - cacheTimestamp < CACHE_DURATION
    ) {
      const processedProducts = processProducts(productsCache);
      const product = processedProducts.find(
        (p) => p.sif_product === parseInt(id)
      );

      if (product) {
        return product;
      }
    }

    // Ako nema cache-a ili proizvod nije pronađen, učitaj sve proizvode
    const response = await axios.get("/external-api/products", {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    // Sačuvaj u cache
    productsCache = response.data;
    cacheTimestamp = Date.now();

    const processedProducts = processProducts(response.data);
    const product = processedProducts.find(
      (p) => p.sif_product === parseInt(id)
    );

    if (!product) {
      throw new Error("Product not found");
    }

    return product;
  },
};

export default api;
