import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "../hooks/useAuth";
import { productsAPI } from "../services/api";
import ProductCard from "./ProductCard";
import SearchFilters from "./SearchFilters";
import LoadingSpinner from "./LoadingSpinner";
import ProductModal from "./ProductModal";
import "./Products.css";

const Products = () => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [productsLoading, setProductsLoading] = useState(false);
  const [error, setError] = useState("");
  const [filters, setFilters] = useState({
    category: "",
    search: "",
  });
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const { logout } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    const isInitialLoad = products.length === 0 && !error;
    fetchProducts(isInitialLoad);
  }, [filters]);

  const fetchProducts = async (isInitialLoad = false) => {
    try {
      if (isInitialLoad) {
        setLoading(true);
      } else {
        setProductsLoading(true);
      }
      setError("");
      const data = await productsAPI.getProducts(filters);
      setProducts(data);
    } catch (err) {
      setError("Greška pri učitavanju proizvoda");
      console.error("Error fetching products:", err);
    } finally {
      if (isInitialLoad) {
        setLoading(false);
      } else {
        setProductsLoading(false);
      }
    }
  };

  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
  };

  const handleLogout = () => {
    logout();
    navigate("/login");
  };

  const handleProductClick = (productId) => {
    const product = products.find((p) => p.sif_product === productId);
    if (product) {
      setSelectedProduct(product);
      setIsModalOpen(true);
    }
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedProduct(null);
  };

  if (loading) {
    return (
      <div className="products-container">
        <LoadingSpinner message="Učitavanje proizvoda..." />
      </div>
    );
  }

  return (
    <div className="products-container">
      <header className="products-header">
        <h1>Proizvodi</h1>
        <button onClick={handleLogout} className="logout-button">
          Odjavi se
        </button>
      </header>

      <SearchFilters
        filters={filters}
        onFilterChange={handleFilterChange}
        products={products}
      />

      {error && <div className="error-message">{error}</div>}

      <div className={`products-grid ${productsLoading ? "loading" : ""}`}>
        {productsLoading ? (
          <div className="products-loading">
            <LoadingSpinner message="Učitavanje proizvoda..." />
          </div>
        ) : products.length === 0 ? (
          <div className="no-products">
            {filters.category || filters.search
              ? "Nema proizvoda koji odgovaraju filterima."
              : "Nema dostupnih proizvoda."}
          </div>
        ) : (
          products.map((product) => (
            <ProductCard
              key={product.sif_product}
              product={product}
              products={products}
              onClick={() => handleProductClick(product.sif_product)}
            />
          ))
        )}
      </div>

      <ProductModal
        product={selectedProduct}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
      />
    </div>
  );
};

export default Products;
