import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    # Eksterni API konfiguracija
    EXTERNAL_API_BASE_URL = "https://zadatak.konovo.rs"
    EXTERNAL_API_USERNAME = "zadatak"
    EXTERNAL_API_PASSWORD = "zadatak"
    
    # Flask konfiguracija
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    DEBUG = True
    
    # CORS konfiguracija
    CORS_ORIGINS = ["http://localhost:3000", "http://localhost:5173", "http://localhost:5174"] 
