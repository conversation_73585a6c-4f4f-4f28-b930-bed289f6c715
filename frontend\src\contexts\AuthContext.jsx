import React, { useState, useEffect } from "react";
import { clearProductsCache } from "../services/api";
import { AuthContext } from "./AuthContextDefinition";

export const AuthProvider = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Proveri da li postoji token u localStorage
    const token = localStorage.getItem("jwt_token");
    if (token) {
      setIsAuthenticated(true);
    }
    setLoading(false);
  }, []);

  const login = (token) => {
    localStorage.setItem("jwt_token", token);
    setIsAuthenticated(true);
  };

  const logout = () => {
    localStorage.removeItem("jwt_token");
    clearProductsCache(); // Obriši cache proizvoda
    setIsAuthenticated(false);
  };

  const value = {
    isAuthenticated,
    login,
    logout,
    loading,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
