.products-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  min-height: 100vh;
  background-color: #f8f9fa;
}

.products-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  background: white;
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.products-header h1 {
  margin: 0;
  color: #333;
  font-size: 2rem;
  font-weight: 700;
}

.logout-button {
  background: #dc3545;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 5px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.2s ease;
}

.logout-button:hover {
  background: #c82333;
  transform: translateY(-2px);
}

.loading {
  text-align: center;
  padding: 3rem;
  font-size: 1.2rem;
  color: #666;
}

.error-message {
  background-color: #fee;
  color: #c33;
  padding: 1rem;
  border-radius: 5px;
  border: 1px solid #fcc;
  text-align: center;
  margin-bottom: 2rem;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.no-products {
  grid-column: 1 / -1;
  text-align: center;
  padding: 3rem;
  background: white;
  border-radius: 10px;
  color: #666;
  font-size: 1.1rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .products-container {
    padding: 1rem;
  }

  .products-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .products-header h1 {
    font-size: 1.5rem;
  }

  .logout-button {
    width: 100%;
  }

  .products-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .products-container {
    padding: 0.5rem;
  }

  .products-header {
    padding: 1rem;
  }

  .products-header h1 {
    font-size: 1.3rem;
  }
}
