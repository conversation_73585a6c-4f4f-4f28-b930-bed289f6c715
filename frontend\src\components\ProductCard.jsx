import "./ProductCard.css";

const ProductCard = ({ product, onClick }) => {
  const formatPrice = (price) => {
    return new Intl.NumberFormat("sr-RS", {
      style: "currency",
      currency: "RSD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(price);
  };

  const truncateDescription = (description, maxLength = 100) => {
    if (!description) return "";
    // Ukloni HTML tagove iz opisa
    const cleanDescription = description
      .replace(/<[^>]*>/g, "")
      .replace(/&[^;]+;/g, " ");
    if (cleanDescription.length <= maxLength) return cleanDescription;
    return cleanDescription.substring(0, maxLength) + "...";
  };

  return (
    <div className="product-card" onClick={onClick}>
      <div className="product-image">
        {product.imgsrc ? (
          <img src={product.imgsrc} alt={product.naziv} />
        ) : (
          <div className="no-image">
            <span>Nema slike</span>
          </div>
        )}
      </div>

      <div className="product-info">
        <h3 className="product-name">{product.naziv}</h3>

        <div className="product-category">
          <span className="category-badge">{product.categoryName}</span>
          {product.brandName && (
            <span className="brand-badge">{product.brandName}</span>
          )}
        </div>

        <p className="product-description">
          {truncateDescription(product.description)}
        </p>

        <div className="product-footer">
          <div className="price-stock-container">
            <span className="product-price">{formatPrice(product.price)}</span>
            {product.stock && (
              <span
                className={`stock-indicator ${
                  parseInt(product.stock) > 0 ? "in-stock" : "out-of-stock"
                }`}
              >
                {parseInt(product.stock) > 0
                  ? `${product.stock} kom`
                  : "Nema na stanju"}
              </span>
            )}
          </div>
          <button onClick={onClick} className="view-details-btn">
            Pogledaj detalje
          </button>
        </div>
      </div>
    </div>
  );
};

export default ProductCard;
