# Konovo Zadatak - Web Aplikacija

Kompletna web aplikacija sa React frontend-om i Python/Flask backend-om.

## Struktura projekta

```
konovo-zadatak/
├── backend/          # Python/Flask API
│   ├── app.py       # Glavna Flask aplikacija
│   ├── requirements.txt
│   └── config.py    # Konfiguracija
├── frontend/        # React aplikacija
│   ├── src/
│   ├── public/
│   └── package.json
└── README.md
```

## Funkcionalnosti

### Backend

- Posrednik za eksterni API (https://zadatak.konovo.rs/)
- JWT autentifikacija
- Obrada podataka proizvoda
- Filtriranje i pretraga
- RESTful API endpoints

### Frontend

- React aplikacija
- Login/logout funkcionalnost
- Prikaz liste proizvoda
- Filtriranje po kategoriji
- Pretraga po nazivu
- Detalji proizvoda

## Pokretanje

### Backend

```bash
cd backend
pip install -r requirements.txt
python app.py
```

### Frontend

```bash
cd frontend
npm install
npm start
```

## API Endpoints

- `POST /api/login` - Prijava korisnika
- `GET /api/products` - Lista proizvoda
- `GET /api/products/search` - Pretraga proizvoda
