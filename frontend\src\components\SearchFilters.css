.search-filters {
  background: white;
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.filters-row {
  display: grid;
  grid-template-columns: 1fr 200px auto;
  gap: 1rem;
  align-items: end;
}

.search-group,
.category-group {
  display: flex;
  flex-direction: column;
}

.search-group label,
.category-group label {
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
}

.search-input,
.category-select {
  padding: 0.75rem;
  border: 2px solid #e1e5e9;
  border-radius: 5px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.search-input:focus,
.category-select:focus {
  outline: none;
  border-color: #667eea;
}

.search-input {
  width: 100%;
}

.category-select {
  cursor: pointer;
}

.clear-filters-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 5px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
  height: fit-content;
}

.clear-filters-btn:hover {
  background: #c82333;
}

.active-filters {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e1e5e9;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  align-items: center;
}

.filters-label {
  font-weight: 500;
  color: #666;
  margin-right: 0.5rem;
}

.filter-tag {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

/* Responsive design */
@media (max-width: 768px) {
  .filters-row {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .clear-filters-btn {
    width: 100%;
  }

  .active-filters {
    flex-direction: column;
    align-items: flex-start;
  }

  .filters-label {
    margin-bottom: 0.5rem;
  }
}

@media (max-width: 480px) {
  .search-filters {
    padding: 1rem;
  }
}
