.product-detail-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  min-height: 100vh;
  background-color: #f8f9fa;
}

.product-detail-header {
  margin-bottom: 2rem;
}

.back-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 5px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.back-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.loading {
  text-align: center;
  padding: 3rem;
  font-size: 1.2rem;
  color: #666;
}

.error-container {
  text-align: center;
  padding: 3rem;
}

.error-message {
  background-color: #fee;
  color: #c33;
  padding: 1rem;
  border-radius: 5px;
  border: 1px solid #fcc;
  margin-bottom: 2rem;
  display: inline-block;
}

.product-detail-card {
  background: white;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0;
}

.product-detail-image {
  height: 500px;
  background-color: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-detail-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.no-image {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #6c757d;
  font-size: 1.2rem;
}

.product-detail-info {
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.product-detail-name {
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
  color: #333;
  line-height: 1.2;
}

.product-detail-category {
  margin: 0;
}

.category-badge {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  margin-right: 0.5rem;
}

.brand-badge {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

.product-detail-price {
  font-size: 2.5rem;
  font-weight: 700;
  color: #28a745;
  margin: 0;
}

.product-meta {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.stock-info,
.sku-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stock-label,
.sku-label {
  font-weight: 600;
  color: #333;
}

.stock-value.in-stock {
  color: #28a745;
  font-weight: 600;
}

.stock-value.out-of-stock {
  color: #dc3545;
  font-weight: 600;
}

.sku-value {
  font-family: "Courier New", monospace;
  background-color: #e9ecef;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.9rem;
}

.product-detail-description h3,
.product-specifications h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 1rem 0;
  border-bottom: 2px solid #e1e5e9;
  padding-bottom: 0.5rem;
}

.product-detail-description .description-content p {
  color: #666;
  line-height: 1.6;
  font-size: 1rem;
  margin: 0.5rem 0;
}

.product-detail-description .description-content p:first-child {
  margin-top: 0;
}

.product-detail-description .description-content p:last-child {
  margin-bottom: 0;
}

.specifications-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.specification-item {
  display: flex;
  justify-content: space-between;
  padding: 0.75rem;
  background-color: #f8f9fa;
  border-radius: 5px;
}

.spec-key {
  font-weight: 600;
  color: #333;
}

.spec-value {
  color: #666;
  text-align: right;
}

/* Responsive design */
@media (max-width: 768px) {
  .product-detail-container {
    padding: 1rem;
  }

  .product-detail-card {
    grid-template-columns: 1fr;
  }

  .product-detail-image {
    height: 300px;
  }

  .product-detail-info {
    padding: 1.5rem;
  }

  .product-detail-name {
    font-size: 1.5rem;
  }

  .product-detail-price {
    font-size: 2rem;
  }

  .specification-item {
    flex-direction: column;
    gap: 0.25rem;
  }

  .spec-value {
    text-align: left;
  }
}

@media (max-width: 480px) {
  .product-detail-container {
    padding: 0.5rem;
  }

  .product-detail-info {
    padding: 1rem;
    gap: 1rem;
  }

  .product-detail-name {
    font-size: 1.3rem;
  }

  .product-detail-price {
    font-size: 1.8rem;
  }
}
