import "./ProductModal.css";

const ProductModal = ({ product, isOpen, onClose }) => {
  if (!isOpen || !product) return null;

  const formatPrice = (price) => {
    return new Intl.NumberFormat("sr-RS", {
      style: "currency",
      currency: "RSD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(price);
  };

  const cleanDescription = (description) => {
    if (!description) return "Nema dostupnog opisa za ovaj proizvod.";
    // Ukloni HTML tagove i entitete, ali zadrži osnovnu strukturu
    return description
      .replace(/<br\s*\/?>/gi, "\n")
      .replace(/<[^>]*>/g, "")
      .replace(/&[^;]+;/g, " ")
      .replace(/\n+/g, "\n")
      .trim();
  };

  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div className="modal-overlay" onClick={handleOverlayClick}>
      <div className="modal-content">
        <div className="modal-header">
          <h2>Detalji proizvoda</h2>
          <button className="modal-close" onClick={onClose}>
            ×
          </button>
        </div>

        <div className="modal-body">
          <div className="product-modal-card">
            <div className="product-modal-image">
              {product.imgsrc ? (
                <img src={product.imgsrc} alt={product.naziv} />
              ) : (
                <div className="no-image">
                  <span>Nema slike</span>
                </div>
              )}
            </div>

            <div className="product-modal-info">
              <h1 className="product-modal-name">{product.naziv}</h1>

              <div className="product-modal-category">
                <span className="category-badge">{product.categoryName}</span>
                {product.brandName && (
                  <span className="brand-badge">{product.brandName}</span>
                )}
              </div>

              <div className="product-modal-price">
                {formatPrice(product.price)}
              </div>

              <div className="product-meta">
                {product.stock && (
                  <div className="stock-info">
                    <span className="stock-label">Na stanju:</span>
                    <span
                      className={`stock-value ${
                        parseInt(product.stock) > 0
                          ? "in-stock"
                          : "out-of-stock"
                      }`}
                    >
                      {parseInt(product.stock) > 0
                        ? `${product.stock} kom`
                        : "Nema na stanju"}
                    </span>
                  </div>
                )}
              </div>

              <div className="product-modal-description">
                <h3>Opis proizvoda</h3>
                <div className="description-content">
                  {cleanDescription(product.description)
                    .split("\n")
                    .map((line, index) => (
                      <p key={index}>{line}</p>
                    ))}
                </div>
              </div>

              {product.specifications && (
                <div className="product-specifications">
                  <h3>Specifikacije</h3>
                  <div className="specifications-list">
                    {Object.entries(product.specifications).map(
                      ([key, value]) => (
                        <div key={key} className="specification-item">
                          <span className="spec-key">{key}:</span>
                          <span className="spec-value">{value}</span>
                        </div>
                      )
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductModal;
