import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { productsAPI } from "../services/api";
import "./ProductDetail.css";

const ProductDetail = () => {
  const [product, setProduct] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  const { id } = useParams();
  const navigate = useNavigate();

  useEffect(() => {
    fetchProduct();
  }, [id]);

  const fetchProduct = async () => {
    try {
      setLoading(true);
      setError("");
      const data = await productsAPI.getProduct(id);
      setProduct(data);
    } catch (err) {
      if (err.response?.status === 404) {
        setError("Proizvod nije pronađen");
      } else {
        setError("Greška pri učitavanju proizvoda");
      }
      console.error("Error fetching product:", err);
    } finally {
      setLoading(false);
    }
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat("sr-RS", {
      style: "currency",
      currency: "RSD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(price);
  };

  const goBack = () => {
    navigate("/products");
  };

  const cleanDescription = (description) => {
    if (!description) return "Nema dostupnog opisa za ovaj proizvod.";
    // Ukloni HTML tagove i entitete, ali zadrži osnovnu strukturu
    return description
      .replace(/<br\s*\/?>/gi, "\n")
      .replace(/<[^>]*>/g, "")
      .replace(/&[^;]+;/g, " ")
      .replace(/\n+/g, "\n")
      .trim();
  };

  if (loading) {
    return (
      <div className="product-detail-container">
        <div className="loading">Učitavanje proizvoda...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="product-detail-container">
        <div className="error-container">
          <div className="error-message">{error}</div>
          <button onClick={goBack} className="back-button">
            Nazad na proizvode
          </button>
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="product-detail-container">
        <div className="error-container">
          <div className="error-message">Proizvod nije pronađen</div>
          <button onClick={goBack} className="back-button">
            Nazad na proizvode
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="product-detail-container">
      <div className="product-detail-header">
        <button onClick={goBack} className="back-button">
          ← Nazad na proizvode
        </button>
      </div>

      <div className="product-detail-card">
        <div className="product-detail-image">
          {product.imgsrc ? (
            <img src={product.imgsrc} alt={product.naziv} />
          ) : (
            <div className="no-image">
              <span>Nema slike</span>
            </div>
          )}
        </div>

        <div className="product-detail-info">
          <h1 className="product-detail-name">{product.naziv}</h1>

          <div className="product-detail-category">
            <span className="category-badge">{product.categoryName}</span>
            {product.brandName && (
              <span className="brand-badge">{product.brandName}</span>
            )}
          </div>

          <div className="product-detail-price">
            {formatPrice(product.price)}
          </div>

          <div className="product-meta">
            {product.stock && (
              <div className="stock-info">
                <span className="stock-label">Na stanju:</span>
                <span
                  className={`stock-value ${
                    parseInt(product.stock) > 0 ? "in-stock" : "out-of-stock"
                  }`}
                >
                  {parseInt(product.stock) > 0
                    ? `${product.stock} kom`
                    : "Nema na stanju"}
                </span>
              </div>
            )}
          </div>

          <div className="product-detail-description">
            <h3>Opis proizvoda</h3>
            <div className="description-content">
              {cleanDescription(product.description)
                .split("\n")
                .map((line, index) => (
                  <p key={index}>{line}</p>
                ))}
            </div>
          </div>

          {product.specifications && (
            <div className="product-specifications">
              <h3>Specifikacije</h3>
              <div className="specifications-list">
                {Object.entries(product.specifications).map(([key, value]) => (
                  <div key={key} className="specification-item">
                    <span className="spec-key">{key}:</span>
                    <span className="spec-value">{value}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProductDetail;
