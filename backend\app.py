from flask import Flask, request, jsonify
from flask_cors import CORS
import requests
import re
from config import Config

app = Flask(__name__)
app.config.from_object(Config)

CORS(app,
     origins=app.config['CORS_ORIGINS'],
     allow_headers=['Content-Type', 'Authorization'],
     methods=['GET', 'POST', 'OPTIONS'],
     supports_credentials=True)


def process_products(products):
    """Obrađuje proizvode prema zahtevima zadatka"""
    processed_products = []
    
    for product in products:
        processed_product = product.copy()
        
        # Uvećaj cenu za 10% za monitore
        category_name = product.get('categoryName', '') or ''
        if category_name.lower() == 'monitori':
            original_price = float(product.get('price', 0))
            processed_product['price'] = round(original_price * 1.1, 2)

        if 'description' in processed_product and processed_product['description']:
            processed_product['description'] = re.sub(
                r'\bbrzina\b', 'performanse',
                processed_product['description'],
                flags=re.IGNORECASE
            )
        
        processed_products.append(processed_product)
    
    return processed_products

@app.route('/api/products', methods=['GET'])
def get_products():
    """Endpoint za dobijanje liste proizvoda"""

    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return jsonify({"error": "Nedostaje autorizacija"}), 401

    user_token = auth_header.split(' ')[1]

    products_url = f"{app.config['EXTERNAL_API_BASE_URL']}/products"
    headers = {"Authorization": f"Bearer {user_token}"}

    try:
        response = requests.get(products_url, headers=headers, verify=False)
        if response.status_code == 200:
            products = response.json()
            processed_products = process_products(products)

            # Filtriranje po kategoriji
            category = request.args.get('category')
            if category:
                processed_products = [p for p in processed_products
                                    if (p.get('categoryName', '') or '').lower() == category.lower()]

            # Pretraga po nazivu
            search = request.args.get('search')
            if search:
                search_lower = search.lower()
                processed_products = [p for p in processed_products
                                    if search_lower in (p.get('naziv', '') or '').lower() or
                                       search_lower in (p.get('description', '') or '').lower()]

            return jsonify(processed_products), 200
        else:
            return jsonify({"error": "Greška pri dobijanju proizvoda"}), response.status_code
    except Exception as e:
        print(f"Greška pri komunikaciji sa eksternim API-jem: {e}")
        return jsonify({"error": f"Greška pri komunikaciji sa eksternim API-jem: {str(e)}"}), 500

@app.route('/api/products/<int:product_id>', methods=['GET'])
def get_product(product_id):
    """Endpoint za dobijanje detalja specifičnog proizvoda"""
    # Proveri autorizaciju
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return jsonify({"error": "Nedostaje autorizacija"}), 401

    user_token = auth_header.split(' ')[1]

    products_url = f"{app.config['EXTERNAL_API_BASE_URL']}/products"
    headers = {"Authorization": f"Bearer {user_token}"}

    try:
        response = requests.get(products_url, headers=headers, verify=False)
        if response.status_code == 200:
            products = response.json()
            processed_products = process_products(products)

            product = next((p for p in processed_products if p.get('sif_product') == product_id), None)

            if product:
                return jsonify(product), 200
            else:
                return jsonify({"error": "Proizvod nije pronađen"}), 404
        else:
            return jsonify({"error": "Greška pri dobijanju proizvoda"}), response.status_code
    except Exception as e:
        return jsonify({"error": "Greška pri komunikaciji sa eksternim API-jem"}), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({"status": "OK", "message": "Backend API je aktivan"}), 200

@app.before_request
def handle_preflight():
    if request.method == "OPTIONS":
        response = jsonify()
        response.headers.add("Access-Control-Allow-Origin", "*")
        response.headers.add('Access-Control-Allow-Headers', "Content-Type,Authorization")
        response.headers.add('Access-Control-Allow-Methods', "GET,PUT,POST,DELETE,OPTIONS")
        return response

if __name__ == '__main__':
    app.run(debug=True, port=5000)
