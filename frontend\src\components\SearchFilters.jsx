import { useState, useEffect, useRef } from "react";
import "./SearchFilters.css";

const SearchFilters = ({ filters, onFilterChange, products }) => {
  const [localFilters, setLocalFilters] = useState(filters);
  const [categories, setCategories] = useState([]);
  const searchTimeoutRef = useRef(null);

  useEffect(() => {
    // Izvuci jedinstvene kategorije iz proizvoda
    const uniqueCategories = [
      ...new Set(products.map((p) => p.categoryName).filter(Boolean)),
    ];
    setCategories(uniqueCategories);
  }, [products]);

  useEffect(() => {
    setLocalFilters(filters);
  }, [filters]);

  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  const handleSearchChange = (e) => {
    const newFilters = { ...localFilters, search: e.target.value };
    setLocalFilters(newFilters);

    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
    searchTimeoutRef.current = setTimeout(() => {
      onFilterChange(newFilters);
    }, 500);
  };

  const handleCategoryChange = (e) => {
    const newFilters = { ...localFilters, category: e.target.value };
    setLocalFilters(newFilters);
    onFilterChange(newFilters);
  };

  const clearFilters = () => {
    const clearedFilters = { category: "", search: "" };
    setLocalFilters(clearedFilters);
    onFilterChange(clearedFilters);
  };

  const hasActiveFilters = localFilters.category || localFilters.search;

  return (
    <div className="search-filters">
      <div className="filters-row">
        <div className="search-group">
          <label htmlFor="search">Pretraga:</label>
          <input
            type="text"
            id="search"
            placeholder="Pretražite proizvode..."
            value={localFilters.search}
            onChange={handleSearchChange}
            className="search-input"
          />
        </div>

        <div className="category-group">
          <label htmlFor="category">Kategorija:</label>
          <select
            id="category"
            value={localFilters.category}
            onChange={handleCategoryChange}
            className="category-select"
          >
            <option value="">Sve kategorije</option>
            {categories.map((category) => (
              <option key={category} value={category}>
                {category}
              </option>
            ))}
          </select>
        </div>

        {hasActiveFilters && (
          <button onClick={clearFilters} className="clear-filters-btn">
            Obriši filtere
          </button>
        )}
      </div>

      {hasActiveFilters && (
        <div className="active-filters">
          <span className="filters-label">Aktivni filteri:</span>
          {localFilters.category && (
            <span className="filter-tag">
              Kategorija: {localFilters.category}
            </span>
          )}
          {localFilters.search && (
            <span className="filter-tag">
              Pretraga: "{localFilters.search}"
            </span>
          )}
        </div>
      )}
    </div>
  );
};

export default SearchFilters;
