.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
}

.modal-content {
  background: white;
  border-radius: 15px;
  max-width: 900px;
  max-height: 90vh;
  width: 100%;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  border-bottom: 1px solid #eee;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.modal-close {
  background: none;
  border: none;
  font-size: 2rem;
  color: white;
  cursor: pointer;
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.modal-close:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.modal-body {
  padding: 0;
  max-height: calc(90vh - 80px);
  overflow-y: auto;
}

.product-modal-card {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0;
  min-height: 500px;
}

.product-modal-image {
  background-color: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30px;
}

.product-modal-image img {
  max-width: 100%;
  max-height: 400px;
  object-fit: contain;
  border-radius: 10px;
}

.no-image {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #6c757d;
  font-size: 1.2rem;
  background-color: #f8f9fa;
  border-radius: 10px;
  width: 100%;
}

.product-modal-info {
  padding: 30px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.product-modal-name {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0;
  color: #333;
  line-height: 1.3;
}

.product-modal-category {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.category-badge {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

.brand-badge {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

.product-modal-price {
  font-size: 2rem;
  font-weight: 700;
  color: #28a745;
  margin: 10px 0;
}

.product-meta {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 10px;
}

.stock-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stock-label {
  font-weight: 600;
  color: #666;
}

.stock-value {
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 0.9rem;
  font-weight: 500;
}

.stock-value.in-stock {
  background-color: #d4edda;
  color: #155724;
}

.stock-value.out-of-stock {
  background-color: #f8d7da;
  color: #721c24;
}

.product-modal-description {
  margin-top: 10px;
}

.product-modal-description h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 1.3rem;
  font-weight: 600;
}

.description-content {
  color: #666;
  line-height: 1.6;
}

.description-content p {
  margin: 0 0 10px 0;
}

.product-specifications h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 1.3rem;
  font-weight: 600;
}

.specifications-list {
  display: grid;
  gap: 10px;
}

.specification-item {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 15px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 5px;
}

.spec-key {
  font-weight: 600;
  color: #666;
}

.spec-value {
  color: #333;
}

/* Responsive design */
@media (max-width: 768px) {
  .modal-overlay {
    padding: 10px;
  }

  .modal-content {
    max-height: 95vh;
  }

  .modal-header {
    padding: 15px 20px;
  }

  .modal-header h2 {
    font-size: 1.3rem;
  }

  .product-modal-card {
    grid-template-columns: 1fr;
  }

  .product-modal-image {
    padding: 20px;
  }

  .product-modal-info {
    padding: 20px;
  }

  .product-modal-name {
    font-size: 1.5rem;
  }

  .product-modal-price {
    font-size: 1.6rem;
  }

  .specification-item {
    grid-template-columns: 1fr;
    gap: 5px;
  }
}
