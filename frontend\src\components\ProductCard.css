.product-card {
  background: white;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.product-image {
  height: 200px;
  overflow: hidden;
  background-color: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.no-image {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #6c757d;
  font-size: 0.9rem;
}

.product-info {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.product-name {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #333;
  line-height: 1.3;
}

.product-category {
  margin-bottom: 0.75rem;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.category-badge {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
}

.brand-badge {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
}

.product-description {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
  margin: 0 0 1rem 0;
  flex-grow: 1;
}

.product-footer {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-top: auto;
}

.price-stock-container {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.product-price {
  font-size: 1.2rem;
  font-weight: 700;
  color: #28a745;
}

.stock-indicator {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.2rem 0.4rem;
  border-radius: 10px;
}

.stock-indicator.in-stock {
  background-color: #d4edda;
  color: #155724;
}

.stock-indicator.out-of-stock {
  background-color: #f8d7da;
  color: #721c24;
}

.view-details-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 5px;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.view-details-btn:hover {
  transform: scale(1.05);
}

/* Responsive design */
@media (max-width: 768px) {
  .product-card {
    margin-bottom: 1rem;
  }

  .product-image {
    height: 150px;
  }

  .product-name {
    font-size: 1rem;
  }

  .product-footer {
    flex-direction: column;
    gap: 0.5rem;
    align-items: stretch;
  }

  .view-details-btn {
    width: 100%;
  }
}
